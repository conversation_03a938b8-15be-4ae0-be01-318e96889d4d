# PostgreSQL configuration for development
# This file contains optimized settings for development use

# Connection settings
listen_addresses = '*'
port = 5432
max_connections = 100

# Memory settings (optimized for development)
shared_buffers = 128MB
effective_cache_size = 256MB
work_mem = 4MB
maintenance_work_mem = 64MB

# WAL settings
wal_buffers = 16MB
checkpoint_completion_target = 0.9

# Query planner settings
random_page_cost = 1.1
effective_io_concurrency = 200

# Logging settings for development
log_destination = 'stderr'
logging_collector = off
log_min_messages = warning
log_min_error_statement = error
log_min_duration_statement = 1000
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on
log_temp_files = 0

# Development-friendly settings
fsync = off
synchronous_commit = off
full_page_writes = off

# Autovacuum settings
autovacuum = on
autovacuum_max_workers = 3
autovacuum_naptime = 1min

# Locale settings
lc_messages = 'en_US.utf8'
lc_monetary = 'en_US.utf8'
lc_numeric = 'en_US.utf8'
lc_time = 'en_US.utf8'

# Default text search config
default_text_search_config = 'pg_catalog.english'
