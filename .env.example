# Database Configuration
DB_HOSTNAME=localhost
DB_PORT=5433
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_NAME=riva_ash_dev

# For Docker setup, use these values:
# DB_HOSTNAME=postgres
# DB_PORT=5432

# Phoenix Configuration
MIX_ENV=dev
PHX_HOST=localhost
PHX_PORT=4000
SECRET_KEY_BASE=EuaQggrb3gfhrDAQUZqTsTMUt7zf9voCI2frB3kuyBabOCHiEue48mXJiMtL7QLj

# Development Configuration
ELIXIR_VERSION=1.18
POSTGRES_VERSION=15

# Docker Configuration
DOCKER_POSTGRES_PORT=5433
DOCKER_POSTGRES_USER=postgres
DOCKER_POSTGRES_PASSWORD=postgres
DOCKER_POSTGRES_DB=riva_ash_dev

# Production Database URL (for production deployment)
# DATABASE_URL=ecto://username:password@hostname:port/database
