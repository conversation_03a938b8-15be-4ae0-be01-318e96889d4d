{"name": "riva_ash_assets", "version": "1.0.0", "description": "Assets for RivaAsh Phoenix application", "private": true, "scripts": {"build": "tailwindcss -i css/app.css -o ../priv/static/assets/app.css && esbuild js/app.js --bundle --outdir=../priv/static/assets --external:/fonts/* --external:/images/*", "build:watch": "concurrently \"tailwindcss -i css/app.css -o ../priv/static/assets/app.css --watch\" \"esbuild js/app.js --bundle --outdir=../priv/static/assets --external:/fonts/* --external:/images/* --watch\"", "deploy": "NODE_ENV=production npm run build"}, "dependencies": {"@tailwindcss/typography": "^0.5.16", "lucide-react": "^0.525.0", "phoenix": "file:../deps/phoenix", "phoenix_html": "file:../deps/phoenix_html", "phoenix_live_view": "file:../deps/phoenix_live_view", "react": "^18.2.0", "react-dom": "^18.2.0", "topbar": "^2.0.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.0", "concurrently": "^8.2.2", "esbuild": "^0.19.0", "postcss": "^8.4.0", "tailwindcss": "^3.3.0"}}