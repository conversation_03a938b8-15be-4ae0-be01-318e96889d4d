defmodule RivaAsh.Repo.Migrations.AddAvailabilityTables do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:items) do
      add(:description, :text)
      add(:is_always_available, :boolean, null: false, default: true)
      add(:capacity, :bigint, null: false, default: 1)
      add(:minimum_duration_minutes, :bigint)
      add(:maximum_duration_minutes, :bigint)
      add(:is_active, :boolean, null: false, default: true)
    end

    create table(:item_schedules, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
      add(:day_of_week, :bigint, null: false)
      add(:start_time, :time, null: false)
      add(:end_time, :time, null: false)
      add(:is_available, :boolean, null: false, default: true)
      add(:notes, :text)

      add(:inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(
        :item_id,
        references(:items,
          column: :id,
          name: "item_schedules_item_id_fkey",
          type: :uuid,
          prefix: "public"
        ),
        null: false
      )
    end

    create(
      unique_index(:item_schedules, [:item_id, :day_of_week, :start_time, :end_time],
        name: "item_schedules_unique_item_day_time_index"
      )
    )

    create table(:availability_exceptions, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
      add(:date, :date, null: false)
      add(:start_time, :time)
      add(:end_time, :time)
      add(:is_available, :boolean, null: false, default: false)
      add(:reason, :text)
      add(:exception_type, :text, default: "other")
      add(:notes, :text)

      add(:inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(
        :item_id,
        references(:items,
          column: :id,
          name: "availability_exceptions_item_id_fkey",
          type: :uuid,
          prefix: "public"
        ),
        null: false
      )
    end

    create(
      unique_index(:availability_exceptions, [:item_id, :date, :start_time, :end_time],
        name: "availability_exceptions_unique_item_date_time_index"
      )
    )
  end

  def down do
    drop_if_exists(
      unique_index(:availability_exceptions, [:item_id, :date, :start_time, :end_time],
        name: "availability_exceptions_unique_item_date_time_index"
      )
    )

    drop(constraint(:availability_exceptions, "availability_exceptions_item_id_fkey"))

    drop(table(:availability_exceptions))

    drop_if_exists(
      unique_index(:item_schedules, [:item_id, :day_of_week, :start_time, :end_time],
        name: "item_schedules_unique_item_day_time_index"
      )
    )

    drop(constraint(:item_schedules, "item_schedules_item_id_fkey"))

    drop(table(:item_schedules))

    alter table(:items) do
      remove(:is_active)
      remove(:maximum_duration_minutes)
      remove(:minimum_duration_minutes)
      remove(:capacity)
      remove(:is_always_available)
      remove(:description)
    end
  end
end
