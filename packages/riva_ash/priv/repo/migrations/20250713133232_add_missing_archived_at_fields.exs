defmodule RivaAsh.Repo.Migrations.AddMissingArchivedAtFields do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:businesses_versions) do
      add(:version_action_name, :text, null: false)
      add(:version_action_inputs, :map, null: false)
      add(:version_resource_identifier, :text, null: false)
    end

    alter table(:permissions) do
      add(:archived_at, :utc_datetime_usec)
    end

    alter table(:employee_permissions) do
      add(:archived_at, :utc_datetime_usec)
    end

    create table(:permissions_versions, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
      add(:version_action_type, :text, null: false)
      add(:version_action_name, :text, null: false)
      add(:version_action_inputs, :map, null: false)
      add(:version_resource_identifier, :text, null: false)

      add(
        :version_source_id,
        references(:permissions,
          column: :id,
          name: "permissions_versions_version_source_id_fkey",
          type: :uuid,
          prefix: "public"
        ), null: false)

      add(:changes, :map)

      add(:version_inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:version_updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )
    end

    alter table(:users) do
      add(:archived_at, :utc_datetime_usec)
    end
  end

  def down do
    alter table(:users) do
      remove(:archived_at)
    end

    drop(constraint(:permissions_versions, "permissions_versions_version_source_id_fkey"))

    drop(table(:permissions_versions))

    alter table(:employee_permissions) do
      remove(:archived_at)
    end

    alter table(:permissions) do
      remove(:archived_at)
    end

    alter table(:businesses_versions) do
      remove(:version_resource_identifier)
      remove(:version_action_inputs)
      remove(:version_action_name)
    end
  end
end
