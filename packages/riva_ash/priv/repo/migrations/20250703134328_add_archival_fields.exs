defmodule RivaAsh.Repo.Migrations.AddArchivalFields do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:sections) do
      add(:archived_at, :utc_datetime_usec)
    end

    alter table(:reservations) do
      add(:archived_at, :utc_datetime_usec)
    end

    alter table(:items) do
      add(:archived_at, :utc_datetime_usec)
    end

    alter table(:item_schedules) do
      add(:archived_at, :utc_datetime_usec)
    end

    alter table(:employees) do
      add(:archived_at, :utc_datetime_usec)
    end

    alter table(:clients) do
      add(:archived_at, :utc_datetime_usec)
    end

    alter table(:businesses) do
      add(:archived_at, :utc_datetime_usec)
    end

    alter table(:availability_exceptions) do
      add(:archived_at, :utc_datetime_usec)
    end
  end

  def down do
    alter table(:availability_exceptions) do
      remove(:archived_at)
    end

    alter table(:businesses) do
      remove(:archived_at)
    end

    alter table(:clients) do
      remove(:archived_at)
    end

    alter table(:employees) do
      remove(:archived_at)
    end

    alter table(:item_schedules) do
      remove(:archived_at)
    end

    alter table(:items) do
      remove(:archived_at)
    end

    alter table(:reservations) do
      remove(:archived_at)
    end

    alter table(:sections) do
      remove(:archived_at)
    end
  end
end
