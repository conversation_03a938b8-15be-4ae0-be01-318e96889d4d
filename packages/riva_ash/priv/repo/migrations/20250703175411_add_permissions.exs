defmodule RivaAsh.Repo.Migrations.AddPermissions do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:permissions, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
      add(:name, :text, null: false)
      add(:description, :text, null: false)
      add(:category, :text, null: false)
      add(:is_assignable, :boolean, null: false, default: true)

      add(:inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )
    end

    create(unique_index(:permissions, [:name], name: "permissions_unique_name_index"))

    create table(:employee_permissions_versions, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
      add(:version_action_type, :text, null: false)
      add(:version_action_name, :text, null: false)
      add(:version_action_inputs, :map, null: false)
      add(:version_resource_identifier, :text, null: false)
      add(:version_source_id, :uuid, null: false)
      add(:changes, :map)

      add(:version_inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:version_updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )
    end

    create table(:employee_permissions, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
    end

    alter table(:employee_permissions_versions) do
      modify(
        :version_source_id,
        references(:employee_permissions,
          column: :id,
          name: "employee_permissions_versions_version_source_id_fkey",
          type: :uuid,
          prefix: "public"
        )
      )
    end

    alter table(:employee_permissions) do
      add(:notes, :text)

      add(:inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(
        :employee_id,
        references(:employees,
          column: :id,
          name: "employee_permissions_employee_id_fkey",
          type: :uuid,
          prefix: "public"
        ),
        null: false
      )

      add(
        :permission_id,
        references(:permissions,
          column: :id,
          name: "employee_permissions_permission_id_fkey",
          type: :uuid,
          prefix: "public"
        ),
        null: false
      )

      add(
        :granted_by_id,
        references(:employees,
          column: :id,
          name: "employee_permissions_granted_by_id_fkey",
          type: :uuid,
          prefix: "public"
        ),
        null: false
      )
    end

    create(
      unique_index(:employee_permissions, [:employee_id, :permission_id],
        name: "employee_permissions_unique_employee_permission_index"
      )
    )
  end

  def down do
    drop_if_exists(
      unique_index(:employee_permissions, [:employee_id, :permission_id],
        name: "employee_permissions_unique_employee_permission_index"
      )
    )

    drop(constraint(:employee_permissions, "employee_permissions_employee_id_fkey"))

    drop(constraint(:employee_permissions, "employee_permissions_permission_id_fkey"))

    drop(constraint(:employee_permissions, "employee_permissions_granted_by_id_fkey"))

    alter table(:employee_permissions) do
      remove(:granted_by_id)
      remove(:permission_id)
      remove(:employee_id)
      remove(:updated_at)
      remove(:inserted_at)
      remove(:notes)
    end

    drop(
      constraint(
        :employee_permissions_versions,
        "employee_permissions_versions_version_source_id_fkey"
      )
    )

    alter table(:employee_permissions_versions) do
      modify(:version_source_id, :uuid)
    end

    drop(table(:employee_permissions))

    drop(table(:employee_permissions_versions))

    drop_if_exists(unique_index(:permissions, [:name], name: "permissions_unique_name_index"))

    drop(table(:permissions))
  end
end
