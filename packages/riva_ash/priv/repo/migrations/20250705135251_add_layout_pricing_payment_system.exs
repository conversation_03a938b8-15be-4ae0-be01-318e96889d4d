defmodule RivaAsh.Repo.Migrations.AddLayoutPricingPaymentSystem do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:reservations) do
      modify(:employee_id, :uuid, null: true)
      add(:is_paid, :boolean, null: false, default: false)
      add(:total_amount, :decimal)
    end

    create table(:pricing, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
      add(:name, :text)
      add(:description, :text)
      add(:pricing_type, :text, default: "base")
      add(:price_per_day, :decimal, null: false)
      add(:currency, :text, null: false, default: "USD")
      add(:effective_from, :date)
      add(:effective_until, :date)
      add(:is_active, :boolean, null: false, default: true)

      add(:inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(
        :business_id,
        references(:businesses,
          column: :id,
          name: "pricing_business_id_fkey",
          type: :uuid,
          prefix: "public"
        ),
        null: false
      )

      add(:item_type_id, :uuid, null: false)
      add(:archived_at, :utc_datetime_usec)
    end

    create table(:payments_versions, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
      add(:version_action_type, :text, null: false)
      add(:version_action_name, :text, null: false)
      add(:version_action_inputs, :map, null: false)
      add(:version_resource_identifier, :text, null: false)
      add(:version_source_id, :uuid, null: false)
      add(:changes, :map)

      add(:version_inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:version_updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )
    end

    create table(:payments, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
    end

    alter table(:payments_versions) do
      modify(
        :version_source_id,
        references(:payments,
          column: :id,
          name: "payments_versions_version_source_id_fkey",
          type: :uuid,
          prefix: "public"
        )
      )
    end

    alter table(:payments) do
      add(:status, :text, default: "pending")
      add(:amount_due, :decimal, null: false)
      add(:amount_paid, :decimal)
      add(:currency, :text, null: false, default: "USD")
      add(:payment_method, :text, default: "cash")
      add(:payment_date, :utc_datetime)
      add(:due_date, :date)
      add(:transaction_reference, :text)
      add(:refund_amount, :decimal)
      add(:refund_date, :utc_datetime)
      add(:refund_reason, :text)
      add(:notes, :text)

      add(:inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(
        :reservation_id,
        references(:reservations,
          column: :id,
          name: "payments_reservation_id_fkey",
          type: :uuid,
          prefix: "public"
        ),
        null: false
      )

      add(
        :pricing_id,
        references(:pricing,
          column: :id,
          name: "payments_pricing_id_fkey",
          type: :uuid,
          prefix: "public"
        )
      )

      add(:archived_at, :utc_datetime_usec)
    end

    create table(:layouts, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
      add(:name, :text, null: false)
      add(:layout_type, :text, default: "grid")
      add(:grid_rows, :bigint)
      add(:grid_columns, :bigint)
      add(:width, :decimal)
      add(:height, :decimal)
      add(:background_color, :text)
      add(:background_image_url, :text)
      add(:is_active, :boolean, null: false, default: true)

      add(:inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(
        :section_id,
        references(:sections,
          column: :id,
          name: "layouts_section_id_fkey",
          type: :uuid,
          prefix: "public"
        ),
        null: false
      )

      add(:archived_at, :utc_datetime_usec)
    end

    create(
      unique_index(:layouts, [:name, :section_id], name: "layouts_unique_name_per_section_index")
    )

    alter table(:items) do
      add(:item_type_id, :uuid)
    end

    create table(:item_types, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
    end

    alter table(:pricing) do
      modify(
        :item_type_id,
        references(:item_types,
          column: :id,
          name: "pricing_item_type_id_fkey",
          type: :uuid,
          prefix: "public"
        )
      )
    end

    create(
      unique_index(:pricing, [:business_id, :item_type_id, :pricing_type],
        name: "pricing_unique_base_pricing_index"
      )
    )

    alter table(:items) do
      modify(
        :item_type_id,
        references(:item_types,
          column: :id,
          name: "items_item_type_id_fkey",
          type: :uuid,
          prefix: "public"
        )
      )
    end

    alter table(:item_types) do
      add(:name, :text, null: false)
      add(:description, :text)
      add(:color, :text)
      add(:icon, :text)
      add(:is_active, :boolean, null: false, default: true)

      add(:inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(
        :business_id,
        references(:businesses,
          column: :id,
          name: "item_types_business_id_fkey",
          type: :uuid,
          prefix: "public"
        ),
        null: false
      )

      add(:archived_at, :utc_datetime_usec)
    end

    create(
      unique_index(:item_types, [:name, :business_id],
        name: "item_types_unique_name_per_business_index"
      )
    )

    create table(:item_positions, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
      add(:grid_row, :bigint)
      add(:grid_column, :bigint)
      add(:x_coordinate, :decimal)
      add(:y_coordinate, :decimal)
      add(:width, :decimal)
      add(:height, :decimal)
      add(:rotation_degrees, :decimal, default: "0")
      add(:z_index, :bigint, default: 0)
      add(:is_visible, :boolean, null: false, default: true)

      add(:inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(
        :item_id,
        references(:items,
          column: :id,
          name: "item_positions_item_id_fkey",
          type: :uuid,
          prefix: "public"
        ),
        null: false
      )

      add(
        :layout_id,
        references(:layouts,
          column: :id,
          name: "item_positions_layout_id_fkey",
          type: :uuid,
          prefix: "public"
        ),
        null: false
      )

      add(:archived_at, :utc_datetime_usec)
    end

    create(
      unique_index(:item_positions, [:layout_id, :grid_row, :grid_column],
        name: "item_positions_unique_grid_position_index"
      )
    )

    create(
      unique_index(:item_positions, [:item_id, :layout_id],
        name: "item_positions_unique_item_per_layout_index"
      )
    )


  end

  def down do


    drop_if_exists(
      unique_index(:item_positions, [:item_id, :layout_id],
        name: "item_positions_unique_item_per_layout_index"
      )
    )

    drop_if_exists(
      unique_index(:item_positions, [:layout_id, :grid_row, :grid_column],
        name: "item_positions_unique_grid_position_index"
      )
    )

    drop(constraint(:item_positions, "item_positions_item_id_fkey"))

    drop(constraint(:item_positions, "item_positions_layout_id_fkey"))

    drop(table(:item_positions))

    drop_if_exists(
      unique_index(:item_types, [:name, :business_id],
        name: "item_types_unique_name_per_business_index"
      )
    )

    drop(constraint(:item_types, "item_types_business_id_fkey"))

    alter table(:item_types) do
      remove(:archived_at)
      remove(:business_id)
      remove(:updated_at)
      remove(:inserted_at)
      remove(:is_active)
      remove(:icon)
      remove(:color)
      remove(:description)
      remove(:name)
    end

    drop(constraint(:items, "items_item_type_id_fkey"))

    alter table(:items) do
      modify(:item_type_id, :uuid)
    end

    drop_if_exists(
      unique_index(:pricing, [:business_id, :item_type_id, :pricing_type],
        name: "pricing_unique_base_pricing_index"
      )
    )

    drop(constraint(:pricing, "pricing_item_type_id_fkey"))

    alter table(:pricing) do
      modify(:item_type_id, :uuid)
    end

    drop(table(:item_types))

    alter table(:items) do
      remove(:item_type_id)
    end

    drop_if_exists(
      unique_index(:layouts, [:name, :section_id], name: "layouts_unique_name_per_section_index")
    )

    drop(constraint(:layouts, "layouts_section_id_fkey"))

    drop(table(:layouts))

    drop(constraint(:payments, "payments_reservation_id_fkey"))

    drop(constraint(:payments, "payments_pricing_id_fkey"))

    alter table(:payments) do
      remove(:archived_at)
      remove(:pricing_id)
      remove(:reservation_id)
      remove(:updated_at)
      remove(:inserted_at)
      remove(:notes)
      remove(:refund_reason)
      remove(:refund_date)
      remove(:refund_amount)
      remove(:transaction_reference)
      remove(:due_date)
      remove(:payment_date)
      remove(:payment_method)
      remove(:currency)
      remove(:amount_paid)
      remove(:amount_due)
      remove(:status)
    end

    drop(constraint(:payments_versions, "payments_versions_version_source_id_fkey"))

    alter table(:payments_versions) do
      modify(:version_source_id, :uuid)
    end

    drop(table(:payments))

    drop(table(:payments_versions))

    drop(constraint(:pricing, "pricing_business_id_fkey"))

    drop(table(:pricing))

    alter table(:reservations) do
      remove(:total_amount)
      remove(:is_paid)
      modify(:employee_id, :uuid, null: false)
    end
  end
end
