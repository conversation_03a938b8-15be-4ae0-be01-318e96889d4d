defmodule RivaAsh.Repo.Migrations.AddPaperTrailVersionTables do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:reservations_versions, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
      add(:version_action_type, :text, null: false)
      add(:version_action_name, :text, null: false)
      add(:version_action_inputs, :map, null: false)
      add(:version_resource_identifier, :text, null: false)

      add(
        :version_source_id,
        references(:reservations,
          column: :id,
          name: "reservations_versions_version_source_id_fkey",
          type: :uuid,
          prefix: "public"
        ),
        null: false
      )

      add(:changes, :map)

      add(:version_inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:version_updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )
    end

    create table(:clients_versions, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
      add(:version_action_type, :text, null: false)
      add(:version_action_name, :text, null: false)
      add(:version_action_inputs, :map, null: false)
      add(:version_resource_identifier, :text, null: false)

      add(
        :version_source_id,
        references(:clients,
          column: :id,
          name: "clients_versions_version_source_id_fkey",
          type: :uuid,
          prefix: "public"
        ),
        null: false
      )

      add(:changes, :map)

      add(:version_inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:version_updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )
    end
  end

  def down do
    drop(constraint(:clients_versions, "clients_versions_version_source_id_fkey"))

    drop(table(:clients_versions))

    drop(constraint(:reservations_versions, "reservations_versions_version_source_id_fkey"))

    drop(table(:reservations_versions))
  end
end
