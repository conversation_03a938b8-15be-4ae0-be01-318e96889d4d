defmodule RivaAsh.Repo.Migrations.AddWeekdayWeekendPricing do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    drop(constraint(:businesses, "businesses_owner_id_fkey"))

    # First, create a dummy user for businesses with null owner_id
    execute("""
      INSERT INTO users (id, email, hashed_password, inserted_at, updated_at)
      SELECT
        gen_random_uuid(),
        '<EMAIL>',
        '$2b$12$dummy.hash.for.system.user',
        NOW(),
        NOW()
      WHERE NOT EXISTS (SELECT 1 FROM users WHERE email = '<EMAIL>')
    """)

    # Update businesses with null owner_id to use the system user
    execute("""
      UPDATE businesses
      SET owner_id = (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1)
      WHERE owner_id IS NULL
    """)

    alter table(:businesses) do
      modify(:owner_id, :uuid, null: false)
    end

    # Check if index exists before creating it
    execute("""
      DO $$
      BEGIN
        IF NOT EXISTS (
          SELECT 1 FROM pg_indexes
          WHERE tablename = 'businesses'
          AND indexname = 'businesses_owner_id_index'
        ) THEN
          CREATE INDEX businesses_owner_id_index ON businesses (owner_id);
        END IF;
      END $$;
    """)

    create table(:pricing_versions, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
      add(:version_action_type, :text, null: false)

      add(
        :version_source_id,
        references(:pricing,
          column: :id,
          name: "pricing_versions_version_source_id_fkey",
          type: :uuid,
          prefix: "public"
        ), null: false)

      add(:changes, :map)

      add(:version_inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:version_updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )
    end

    create table(:item_types_versions, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
      add(:version_action_type, :text, null: false)

      add(
        :version_source_id,
        references(:item_types,
          column: :id,
          name: "item_types_versions_version_source_id_fkey",
          type: :uuid,
          prefix: "public"
        ), null: false)

      add(:changes, :map)

      add(:version_inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:version_updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )
    end

    create table(:sections_versions, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
      add(:version_action_type, :text, null: false)

      add(
        :version_source_id,
        references(:sections,
          column: :id,
          name: "sections_versions_version_source_id_fkey",
          type: :uuid,
          prefix: "public"
        ), null: false)

      add(:changes, :map)

      add(:version_inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:version_updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )
    end

    create table(:items_versions, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
      add(:version_action_type, :text, null: false)

      add(
        :version_source_id,
        references(:items,
          column: :id,
          name: "items_versions_version_source_id_fkey",
          type: :uuid,
          prefix: "public"
        ), null: false)

      add(:changes, :map)

      add(:version_inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:version_updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )
    end

    alter table(:items) do
      modify(:is_always_available, :boolean, default: false)
      modify(:is_active, :boolean, null: true)

      add(:business_id, :uuid, null: true)
    end

    # Update items with null business_id to use the first available business
    execute("""
      UPDATE items
      SET business_id = (SELECT id FROM businesses LIMIT 1)
      WHERE business_id IS NULL
    """)

    # Now make the column non-null and add the foreign key constraint
    alter table(:items) do
      modify(:business_id, :uuid, null: false)
    end

    execute("ALTER TABLE items ADD CONSTRAINT items_business_id_fkey FOREIGN KEY (business_id) REFERENCES businesses(id)")


    drop_if_exists(unique_index(:items, [:name], name: "items_unique_name_index"))

    create(
      unique_index(:items, [:name, :business_id], name: "items_unique_name_per_business_index")
    )

    create table(:businesses_versions, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
      add(:version_action_type, :text, null: false)

      add(
        :version_source_id,
        references(:businesses,
          column: :id,
          name: "businesses_versions_version_source_id_fkey",
          type: :uuid,
          prefix: "public"
        ), null: false)

      add(:changes, :map)

      add(:version_inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:version_updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )
    end

    drop(constraint(:layouts, "layouts_section_id_fkey"))

    drop_if_exists(
      unique_index(:layouts, [:name, :section_id], name: "layouts_unique_name_per_section_index")
    )

    rename(table(:layouts), :section_id, to: :plot_id)

    create table(:plots_versions, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
      add(:version_action_type, :text, null: false)
      add(:version_source_id, :uuid, null: false)
      add(:changes, :map)

      add(:version_inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:version_updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )
    end

    create table(:plots, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
    end

    alter table(:layouts) do
      modify(
        :plot_id,
        references(:plots,
          column: :id,
          name: "layouts_plot_id_fkey",
          type: :uuid,
          prefix: "public"
        )
      )
    end

    create(unique_index(:layouts, [:name, :plot_id], name: "layouts_unique_name_per_plot_index"))

    alter table(:plots_versions) do
      modify(
        :version_source_id,
        references(:plots,
          column: :id,
          name: "plots_versions_version_source_id_fkey",
          type: :uuid,
          prefix: "public"
        )
      )
    end

    alter table(:plots) do
      add(:name, :text, null: false)
      add(:description, :text)
      add(:address, :text)
      add(:total_area, :decimal)
      add(:area_unit, :text, default: "sqft")
      add(:coordinates, :map)
      add(:is_active, :boolean, null: false, default: true)

      add(:inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(
        :business_id,
        references(:businesses,
          column: :id,
          name: "plots_business_id_fkey",
          type: :uuid,
          prefix: "public"
        ), null: false)

      add(:archived_at, :utc_datetime_usec)
    end

    create(
      unique_index(:plots, [:name, :business_id], name: "plots_unique_name_per_business_index")
    )

    alter table(:payments) do
      add(
        :business_id,
        references(:businesses,
          column: :id,
          name: "payments_business_id_fkey",
          type: :uuid,
          prefix: "public"
        ), null: false)
    end

    create table(:item_holds, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
      add(:reserved_from, :utc_datetime, null: false)
      add(:reserved_until, :utc_datetime, null: false)
      add(:expires_at, :utc_datetime, null: false)
      add(:hold_duration_minutes, :bigint, null: false, default: 15)
      add(:is_active, :boolean, null: false, default: true)
      add(:released_at, :utc_datetime)
      add(:notes, :text)

      add(:inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(
        :item_id,
        references(:items,
          column: :id,
          name: "item_holds_item_id_fkey",
          type: :uuid,
          prefix: "public"
        ), null: false)

      add(
        :client_id,
        references(:clients,
          column: :id,
          name: "item_holds_client_id_fkey",
          type: :uuid,
          prefix: "public"
        ), null: false)

      add(:archived_at, :utc_datetime_usec)
    end

    create(
      unique_index(:item_holds, [:item_id, :reserved_from, :reserved_until, :is_active],
        name: "item_holds_unique_active_hold_index"
      )
    )

    create table(:layouts_versions, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
      add(:version_action_type, :text, null: false)

      add(
        :version_source_id,
        references(:layouts,
          column: :id,
          name: "layouts_versions_version_source_id_fkey",
          type: :uuid,
          prefix: "public"
        ), null: false)

      add(:changes, :map)

      add(:version_inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:version_updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )
    end

    alter table(:pricing) do
      add(:weekday_price, :decimal)
      add(:weekend_price, :decimal)
      add(:has_day_type_pricing, :boolean, null: false, default: false)
    end

    drop_if_exists(
      unique_index(:pricing, [:business_id, :item_type_id, :pricing_type],
        name: "pricing_unique_base_pricing_index"
      )
    )

    create(
      unique_index(
        :pricing,
        [:business_id, :item_type_id, :pricing_type, :effective_from, :effective_until],
        name: "pricing_unique_pricing_rule_index"
      )
    )

    drop(constraint(:sections, "sections_business_id_fkey"))

    drop_if_exists(
      unique_index(:sections, [:name, :business_id],
        name: "sections_unique_name_per_business_index"
      )
    )

    # First, create a default plot for each business that has sections
    execute("""
      INSERT INTO plots (id, name, business_id, inserted_at, updated_at)
      SELECT
        gen_random_uuid(),
        'Default Plot',
        business_id,
        NOW(),
        NOW()
      FROM (
        SELECT DISTINCT business_id
        FROM sections
        WHERE business_id IS NOT NULL
      ) AS business_ids
      WHERE NOT EXISTS (
        SELECT 1 FROM plots WHERE plots.business_id = business_ids.business_id
      )
    """)

    # Update sections to reference the plot for their business
    execute("""
      UPDATE sections
      SET business_id = (
        SELECT id FROM plots
        WHERE plots.business_id = sections.business_id
        LIMIT 1
      )
      WHERE business_id IS NOT NULL
    """)

    rename(table(:sections), :business_id, to: :plot_id)

    alter table(:sections) do
      modify(
        :plot_id,
        references(:plots,
          column: :id,
          name: "sections_plot_id_fkey",
          type: :uuid,
          prefix: "public"
        )
      )
    end

    create(
      unique_index(:sections, [:name, :plot_id], name: "sections_unique_name_per_plot_index")
    )

    alter table(:clients) do
      add(
        :business_id,
        references(:businesses,
          column: :id,
          name: "clients_business_id_fkey",
          type: :uuid,
          prefix: "public"
        ), null: false)
    end

    drop_if_exists(unique_index(:clients, [:email], name: "clients_email_index"))

    create(
      unique_index(:clients, [:business_id, :email],
        name: "clients_unique_email_per_business_index"
      )
    )

    alter table(:reservations) do
      add(
        :business_id,
        references(:businesses,
          column: :id,
          name: "reservations_business_id_fkey",
          type: :uuid,
          prefix: "public"
        ), null: false)

      add(:hold_expires_at, :utc_datetime)
      add(:is_provisional, :boolean, null: false, default: false)
      add(:number_of_days, :bigint)
      add(:daily_rate, :decimal)
      add(:multi_day_discount, :decimal)
    end

    create(
      unique_index(:reservations, [:item_id, :reserved_from, :reserved_until],
        name: "reservations_unique_item_time_slot_index"
      )
    )
  end

  def down do
    drop_if_exists(
      unique_index(:reservations, [:item_id, :reserved_from, :reserved_until],
        name: "reservations_unique_item_time_slot_index"
      )
    )

    drop(constraint(:reservations, "reservations_business_id_fkey"))

    alter table(:reservations) do
      remove(:multi_day_discount)
      remove(:daily_rate)
      remove(:number_of_days)
      remove(:is_provisional)
      remove(:hold_expires_at)
      remove(:business_id)
    end

    drop_if_exists(
      unique_index(:clients, [:business_id, :email],
        name: "clients_unique_email_per_business_index"
      )
    )

    create(unique_index(:clients, [:email], name: "clients_email_index"))

    drop(constraint(:clients, "clients_business_id_fkey"))

    alter table(:clients) do
      remove(:business_id)
    end

    drop_if_exists(
      unique_index(:sections, [:name, :plot_id], name: "sections_unique_name_per_plot_index")
    )

    drop(constraint(:sections, "sections_plot_id_fkey"))

    alter table(:sections) do
      modify(
        :business_id,
        references(:businesses,
          column: :id,
          name: "sections_business_id_fkey",
          type: :uuid,
          prefix: "public"
        )
      )
    end

    rename(table(:sections), :plot_id, to: :business_id)

    create(
      unique_index(:sections, [:name, :business_id],
        name: "sections_unique_name_per_business_index"
      )
    )

    drop_if_exists(
      unique_index(
        :pricing,
        [:business_id, :item_type_id, :pricing_type, :effective_from, :effective_until],
        name: "pricing_unique_pricing_rule_index"
      )
    )

    create(
      unique_index(:pricing, [:business_id, :item_type_id, :pricing_type],
        name: "pricing_unique_base_pricing_index"
      )
    )

    alter table(:pricing) do
      remove(:has_day_type_pricing)
      remove(:weekend_price)
      remove(:weekday_price)
    end

    drop(constraint(:layouts_versions, "layouts_versions_version_source_id_fkey"))

    drop(table(:layouts_versions))

    drop_if_exists(
      unique_index(:item_holds, [:item_id, :reserved_from, :reserved_until, :is_active],
        name: "item_holds_unique_active_hold_index"
      )
    )

    drop(constraint(:item_holds, "item_holds_item_id_fkey"))

    drop(constraint(:item_holds, "item_holds_client_id_fkey"))

    drop(table(:item_holds))

    drop(constraint(:payments, "payments_business_id_fkey"))

    alter table(:payments) do
      remove(:business_id)
    end

    drop_if_exists(
      unique_index(:plots, [:name, :business_id], name: "plots_unique_name_per_business_index")
    )

    drop(constraint(:plots, "plots_business_id_fkey"))

    alter table(:plots) do
      remove(:archived_at)
      remove(:business_id)
      remove(:updated_at)
      remove(:inserted_at)
      remove(:is_active)
      remove(:coordinates)
      remove(:area_unit)
      remove(:total_area)
      remove(:address)
      remove(:description)
      remove(:name)
    end

    drop(constraint(:plots_versions, "plots_versions_version_source_id_fkey"))

    alter table(:plots_versions) do
      modify(:version_source_id, :uuid)
    end

    drop_if_exists(
      unique_index(:layouts, [:name, :plot_id], name: "layouts_unique_name_per_plot_index")
    )

    drop(constraint(:layouts, "layouts_plot_id_fkey"))

    alter table(:layouts) do
      modify(
        :section_id,
        references(:sections,
          column: :id,
          name: "layouts_section_id_fkey",
          type: :uuid,
          prefix: "public"
        )
      )
    end

    drop(table(:plots))

    drop(table(:plots_versions))

    rename(table(:layouts), :plot_id, to: :section_id)

    create(
      unique_index(:layouts, [:name, :section_id], name: "layouts_unique_name_per_section_index")
    )

    drop(constraint(:businesses_versions, "businesses_versions_version_source_id_fkey"))

    drop(table(:businesses_versions))

    drop_if_exists(
      unique_index(:items, [:name, :business_id], name: "items_unique_name_per_business_index")
    )

    create(unique_index(:items, [:name], name: "items_unique_name_index"))

    drop(constraint(:items, "items_business_id_fkey"))

    alter table(:items) do
      remove(:business_id)
      modify(:is_active, :boolean, null: false)
      modify(:is_always_available, :boolean, default: true)
    end

    drop(constraint(:items_versions, "items_versions_version_source_id_fkey"))

    drop(table(:items_versions))

    drop(constraint(:sections_versions, "sections_versions_version_source_id_fkey"))

    drop(table(:sections_versions))

    drop(constraint(:item_types_versions, "item_types_versions_version_source_id_fkey"))

    drop(table(:item_types_versions))

    drop(constraint(:pricing_versions, "pricing_versions_version_source_id_fkey"))

    drop(table(:pricing_versions))

    drop_if_exists(index(:businesses, [:owner_id]))

    alter table(:businesses) do
      modify(
        :owner_id,
        references(:users,
          column: :id,
          name: "businesses_owner_id_fkey",
          type: :uuid,
          prefix: "public"
        ), null: true)
    end
  end
end
