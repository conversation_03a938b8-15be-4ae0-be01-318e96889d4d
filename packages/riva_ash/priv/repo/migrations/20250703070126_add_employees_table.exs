defmodule RivaAsh.Repo.Migrations.AddEmployeesTable do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:reservations) do
      add(:employee_id, :uuid, null: false)
    end

    create table(:employees_versions, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
      add(:version_action_type, :text, null: false)
      add(:version_action_name, :text, null: false)
      add(:version_action_inputs, :map, null: false)
      add(:version_resource_identifier, :text, null: false)
      add(:version_source_id, :uuid, null: false)
      add(:changes, :map)

      add(:version_inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:version_updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )
    end

    create table(:employees, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
    end

    alter table(:reservations) do
      modify(
        :employee_id,
        references(:employees,
          column: :id,
          name: "reservations_employee_id_fkey",
          type: :uuid,
          prefix: "public"
        )
      )
    end

    alter table(:employees_versions) do
      modify(
        :version_source_id,
        references(:employees,
          column: :id,
          name: "employees_versions_version_source_id_fkey",
          type: :uuid,
          prefix: "public"
        )
      )
    end

    alter table(:employees) do
      add(:email, :citext, null: false)
      add(:first_name, :text, null: false)
      add(:last_name, :text, null: false)
      add(:phone, :text)
      add(:role, :text, default: "staff")
      add(:is_active, :boolean, null: false, default: true)
      add(:employee_number, :text)
      add(:hire_date, :date)
      add(:last_login_at, :utc_datetime)
      add(:notes, :text)

      add(:inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(
        :business_id,
        references(:businesses,
          column: :id,
          name: "employees_business_id_fkey",
          type: :uuid,
          prefix: "public"
        ),
        null: false
      )
    end

    create(unique_index(:employees, [:email], name: "employees_unique_email_index"))

    create(
      unique_index(:employees, [:business_id, :employee_number],
        name: "employees_unique_employee_number_per_business_index",
        where: "(employee_number IS NOT NULL)"
      )
    )
  end

  def down do
    drop_if_exists(
      unique_index(:employees, [:business_id, :employee_number],
        name: "employees_unique_employee_number_per_business_index"
      )
    )

    drop_if_exists(unique_index(:employees, [:email], name: "employees_unique_email_index"))

    drop(constraint(:employees, "employees_business_id_fkey"))

    alter table(:employees) do
      remove(:business_id)
      remove(:updated_at)
      remove(:inserted_at)
      remove(:notes)
      remove(:last_login_at)
      remove(:hire_date)
      remove(:employee_number)
      remove(:is_active)
      remove(:role)
      remove(:phone)
      remove(:last_name)
      remove(:first_name)
      remove(:email)
    end

    drop(constraint(:employees_versions, "employees_versions_version_source_id_fkey"))

    alter table(:employees_versions) do
      modify(:version_source_id, :uuid)
    end

    drop(constraint(:reservations, "reservations_employee_id_fkey"))

    alter table(:reservations) do
      modify(:employee_id, :uuid)
    end

    drop(table(:employees))

    drop(table(:employees_versions))

    alter table(:reservations) do
      remove(:employee_id)
    end
  end
end
