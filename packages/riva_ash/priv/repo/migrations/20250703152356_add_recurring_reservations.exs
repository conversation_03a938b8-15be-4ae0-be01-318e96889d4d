defmodule RivaAsh.Repo.Migrations.AddRecurringReservations do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:recurring_reservations_versions, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
      add(:version_action_type, :text, null: false)
      add(:version_action_name, :text, null: false)
      add(:version_action_inputs, :map, null: false)
      add(:version_resource_identifier, :text, null: false)
      add(:version_source_id, :uuid, null: false)
      add(:changes, :map)

      add(:version_inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:version_updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )
    end

    create table(:recurring_reservations, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
    end

    alter table(:recurring_reservations_versions) do
      modify(
        :version_source_id,
        references(:recurring_reservations,
          column: :id,
          name: "recurring_reservations_versions_version_source_id_fkey",
          type: :uuid,
          prefix: "public"
        )
      )
    end

    alter table(:recurring_reservations) do
      add(:title, :text)
      add(:start_date, :date, null: false)
      add(:start_time, :time, null: false)
      add(:end_time, :time, null: false)
      add(:consecutive_days, :bigint, null: false)
      add(:pattern_type, :text, default: "consecutive_days")
      add(:status, :text, default: "pending")
      add(:notes, :text)
      add(:instances_generated, :boolean, null: false, default: false)
      add(:generated_at, :utc_datetime)

      add(:inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(
        :client_id,
        references(:clients,
          column: :id,
          name: "recurring_reservations_client_id_fkey",
          type: :uuid,
          prefix: "public"
        ),
        null: false
      )

      add(
        :item_id,
        references(:items,
          column: :id,
          name: "recurring_reservations_item_id_fkey",
          type: :uuid,
          prefix: "public"
        ),
        null: false
      )

      add(
        :employee_id,
        references(:employees,
          column: :id,
          name: "recurring_reservations_employee_id_fkey",
          type: :uuid,
          prefix: "public"
        ),
        null: false
      )

      add(:archived_at, :utc_datetime_usec)
    end

    create table(:recurring_reservation_instances_versions, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
      add(:version_action_type, :text, null: false)
      add(:version_action_name, :text, null: false)
      add(:version_action_inputs, :map, null: false)
      add(:version_resource_identifier, :text, null: false)
      add(:version_source_id, :uuid, null: false)
      add(:changes, :map)

      add(:version_inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:version_updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )
    end

    create table(:recurring_reservation_instances, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
    end

    alter table(:recurring_reservation_instances_versions) do
      modify(
        :version_source_id,
        references(:recurring_reservation_instances,
          column: :id,
          name: "recurring_reservation_instances_versions_version_source_id_fkey",
          type: :uuid,
          prefix: "public"
        )
      )
    end

    alter table(:recurring_reservation_instances) do
      add(:scheduled_date, :date, null: false)
      add(:sequence_number, :bigint, null: false)
      add(:status, :text, default: "pending")
      add(:notes, :text)
      add(:skip_reason, :text)
      add(:error_message, :text)
      add(:created_at, :utc_datetime)
      add(:failed_at, :utc_datetime)

      add(:inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(
        :recurring_reservation_id,
        references(:recurring_reservations,
          column: :id,
          name: "recurring_reservation_instances_recurring_reservation_id_fkey",
          type: :uuid,
          prefix: "public"
        ),
        null: false
      )

      add(
        :reservation_id,
        references(:reservations,
          column: :id,
          name: "recurring_reservation_instances_reservation_id_fkey",
          type: :uuid,
          prefix: "public"
        )
      )

      add(:archived_at, :utc_datetime_usec)
    end

    create(
      unique_index(:recurring_reservation_instances, [:recurring_reservation_id, :scheduled_date],
        name: "recurring_reservation_instances_unique_recurring_date_index"
      )
    )

    create(
      unique_index(
        :recurring_reservation_instances,
        [:recurring_reservation_id, :sequence_number],
        name: "recurring_reservation_instances_unique_recurring_sequence_index"
      )
    )
  end

  def down do
    drop_if_exists(
      unique_index(
        :recurring_reservation_instances,
        [:recurring_reservation_id, :sequence_number],
        name: "recurring_reservation_instances_unique_recurring_sequence_index"
      )
    )

    drop_if_exists(
      unique_index(:recurring_reservation_instances, [:recurring_reservation_id, :scheduled_date],
        name: "recurring_reservation_instances_unique_recurring_date_index"
      )
    )

    drop(
      constraint(
        :recurring_reservation_instances,
        "recurring_reservation_instances_recurring_reservation_id_fkey"
      )
    )

    drop(
      constraint(
        :recurring_reservation_instances,
        "recurring_reservation_instances_reservation_id_fkey"
      )
    )

    alter table(:recurring_reservation_instances) do
      remove(:archived_at)
      remove(:reservation_id)
      remove(:recurring_reservation_id)
      remove(:updated_at)
      remove(:inserted_at)
      remove(:failed_at)
      remove(:created_at)
      remove(:error_message)
      remove(:skip_reason)
      remove(:notes)
      remove(:status)
      remove(:sequence_number)
      remove(:scheduled_date)
    end

    drop(
      constraint(
        :recurring_reservation_instances_versions,
        "recurring_reservation_instances_versions_version_source_id_fkey"
      )
    )

    alter table(:recurring_reservation_instances_versions) do
      modify(:version_source_id, :uuid)
    end

    drop(table(:recurring_reservation_instances))

    drop(table(:recurring_reservation_instances_versions))

    drop(constraint(:recurring_reservations, "recurring_reservations_client_id_fkey"))

    drop(constraint(:recurring_reservations, "recurring_reservations_item_id_fkey"))

    drop(constraint(:recurring_reservations, "recurring_reservations_employee_id_fkey"))

    alter table(:recurring_reservations) do
      remove(:archived_at)
      remove(:employee_id)
      remove(:item_id)
      remove(:client_id)
      remove(:updated_at)
      remove(:inserted_at)
      remove(:generated_at)
      remove(:instances_generated)
      remove(:notes)
      remove(:status)
      remove(:pattern_type)
      remove(:consecutive_days)
      remove(:end_time)
      remove(:start_time)
      remove(:start_date)
      remove(:title)
    end

    drop(
      constraint(
        :recurring_reservations_versions,
        "recurring_reservations_versions_version_source_id_fkey"
      )
    )

    alter table(:recurring_reservations_versions) do
      modify(:version_source_id, :uuid)
    end

    drop(table(:recurring_reservations))

    drop(table(:recurring_reservations_versions))
  end
end
