{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "precision": null, "primary_key?": true, "references": null, "scale": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "date", "type": "date"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "start_time", "type": "time"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "end_time", "type": "time"}, {"allow_nil?": false, "default": "false", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "is_available", "type": "boolean"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "reason", "type": "text"}, {"allow_nil?": true, "default": "\"other\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "exception_type", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "notes", "type": "text"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "availability_exceptions_item_id_fkey", "on_delete": null, "on_update": null, "primary_key?": true, "schema": "public", "table": "items"}, "scale": null, "size": null, "source": "item_id", "type": "uuid"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "archived_at", "type": "utc_datetime_usec"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "1A778BB1E71F828FF5FAAE97643F127A3293E3C2B3BDD28F35E79EFEAF4BA69A", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "availability_exceptions_unique_item_date_time_index", "keys": [{"type": "atom", "value": "item_id"}, {"type": "atom", "value": "date"}, {"type": "atom", "value": "start_time"}, {"type": "atom", "value": "end_time"}], "name": "unique_item_date_time", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.RivaAsh.Repo", "schema": null, "table": "availability_exceptions"}