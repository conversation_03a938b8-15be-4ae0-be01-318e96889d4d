{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "precision": null, "primary_key?": true, "references": null, "scale": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "notes", "type": "text"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "employee_permissions_employee_id_fkey", "on_delete": null, "on_update": null, "primary_key?": true, "schema": "public", "table": "employees"}, "scale": null, "size": null, "source": "employee_id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "employee_permissions_permission_id_fkey", "on_delete": null, "on_update": null, "primary_key?": true, "schema": "public", "table": "permissions"}, "scale": null, "size": null, "source": "permission_id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "employee_permissions_granted_by_id_fkey", "on_delete": null, "on_update": null, "primary_key?": true, "schema": "public", "table": "employees"}, "scale": null, "size": null, "source": "granted_by_id", "type": "uuid"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "D1AC8EB28297FB0E5769F6531C8DCA233C0BE15A650DD346A96B7F2565D983D9", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "employee_permissions_unique_employee_permission_index", "keys": [{"type": "atom", "value": "employee_id"}, {"type": "atom", "value": "permission_id"}], "name": "unique_employee_permission", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.RivaAsh.Repo", "schema": null, "table": "employee_permissions"}