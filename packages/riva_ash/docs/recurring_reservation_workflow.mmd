flowchart TD
    %% Recurring Reservation Workflow Diagram
    
    Start([Start: Create Recurring Reservation]) --> ValidateInputs{Validate Inputs}
    
    ValidateInputs -->|Invalid| Error1[Return Validation Error]
    ValidateInputs -->|Valid| GetClient[Get Client by ID]
    
    GetClient -->|Not Found| Error2[Return Client Not Found]
    GetClient -->|Found| GetEmployee[Get Employee by ID]
    
    GetEmployee -->|Not Found| Error3[Return Employee Not Found]
    GetEmployee -->|Found| GetItem[Get Item by ID]
    
    GetItem -->|Not Found| Error4[Return Item Not Found]
    GetItem -->|Found| CreateRecurring[Create Recurring Reservation]
    
    CreateRecurring -->|Success| CalculateDates[Calculate Consecutive Dates]
    CreateRecurring -->|Failed| Error5[Return Creation Error]
    
    CalculateDates --> CreateInstances[Create Reservation Instances]
    
    CreateInstances -->|Success| UpdateStatus[Update Status to Active]
    CreateInstances -->|Failed| Error6[Return Instance Creation Error]
    
    UpdateStatus --> Success([Success: Return Recurring Reservation])
    
    %% Instance Processing Workflow
    Success --> ProcessInstances{Process Individual Instances?}
    ProcessInstances -->|Yes| SelectInstance[Select Pending Instance]
    ProcessInstances -->|No| End([End])
    
    SelectInstance --> CheckAvailability{Check Item Availability}
    
    CheckAvailability -->|Available| CreateReservation[Create Actual Reservation]
    CheckAvailability -->|Not Available| MarkFailed[Mark Instance as Failed]
    
    CreateReservation -->|Success| LinkReservation[Link Reservation to Instance]
    CreateReservation -->|Failed| MarkFailed
    
    LinkReservation --> MarkConfirmed[Mark Instance as Confirmed]
    MarkFailed --> NextInstance{More Pending Instances?}
    MarkConfirmed --> NextInstance
    
    NextInstance -->|Yes| SelectInstance
    NextInstance -->|No| End
    
    %% Error handling
    Error1 --> End
    Error2 --> End
    Error3 --> End
    Error4 --> End
    Error5 --> End
    Error6 --> End
    
    %% Styling
    classDef errorClass fill:#ffcccc,stroke:#ff0000,stroke-width:2px
    classDef successClass fill:#ccffcc,stroke:#00ff00,stroke-width:2px
    classDef processClass fill:#cceeff,stroke:#0066cc,stroke-width:2px
    classDef decisionClass fill:#ffffcc,stroke:#ffcc00,stroke-width:2px
    
    class Error1,Error2,Error3,Error4,Error5,Error6,MarkFailed errorClass
    class Success,End,MarkConfirmed successClass
    class CreateRecurring,CalculateDates,CreateInstances,CreateReservation,LinkReservation processClass
    class ValidateInputs,CheckAvailability,ProcessInstances,NextInstance decisionClass
