flowchart TD
    %% Recurring Reservation Reactor Diagram
    
    Start([Start: RecurringReservationReactor]) --> InputId[Input: recurring_reservation_id]
    
    InputId --> GetRecurring[Step: get_recurring_reservation<br/>Read from database]
    
    GetRecurring --> CalculateDates[Step: calculate_dates<br/>Generate consecutive dates]
    
    CalculateDates --> CreateInstances[Step: create_instances<br/>Batch create instances]
    
    CreateInstances --> UpdateStatus[Step: update_status<br/>Mark as active]
    
    UpdateStatus --> Return([Return: Updated Recurring Reservation])
    
    %% Instance Processing Sub-flow
    Return --> ProcessLoop{Process Instances?}
    ProcessLoop -->|Yes| SelectInstance[Step: select_instance<br/>Get next pending]
    ProcessLoop -->|No| End([End])
    
    SelectInstance --> CheckAvailability[Step: check_availability<br/>Validate time slot]
    
    CheckAvailability -->|Available| CreateReservation[Step: create_reservation<br/>Make actual booking]
    CheckAvailability -->|Unavailable| MarkFailed[Step: mark_failed<br/>Update instance status]
    
    CreateReservation --> LinkReservation[Step: link_reservation<br/>Connect instance to reservation]
    
    LinkReservation --> MarkConfirmed[Step: mark_confirmed<br/>Update instance status]
    
    MarkFailed --> HasMore{More Instances?}
    MarkConfirmed --> HasMore
    
    HasMore -->|Yes| SelectInstance
    HasMore -->|No| End
    
    %% Error Handling
    GetRecurring -->|Error| ErrorHandler[Error: Handle Failure]
    CalculateDates -->|Error| ErrorHandler
    CreateInstances -->|Error| ErrorHandler
    CreateReservation -->|Error| MarkFailed
    
    ErrorHandler --> End
    
    %% Styling
    classDef inputClass fill:#e1f5fe,stroke:#0277bd,stroke-width:2px
    classDef stepClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef decisionClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef errorClass fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef returnClass fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    
    class InputId inputClass
    class GetRecurring,CalculateDates,CreateInstances,UpdateStatus,SelectInstance,CheckAvailability,CreateReservation,LinkReservation,MarkConfirmed,MarkFailed stepClass
    class ProcessLoop,HasMore decisionClass
    class ErrorHandler errorClass
    class Start,Return,End returnClass
